import { PageLayout } from "@/components/page-layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"

export default function UnauthorizedPage() {
  return (
    <PageLayout title="Unauthorized Access" showNavigation={false}>
      <div className="text-center space-y-6 py-12">
        <div className="space-y-2">
          <h1 className="text-6xl font-bold text-destructive">401</h1>
          <h2 className="text-2xl font-semibold text-foreground">Unauthorized Access</h2>
          <p className="text-muted-foreground max-w-md mx-auto">
            You do not have permission to access this resource. Please sign in with appropriate credentials.
          </p>
        </div>

        <div className="flex justify-center gap-4">
          <Link href="/signin">
            <Button>Sign In</Button>
          </Link>
          <Link href="/">
            <Button variant="outline">Go Home</Button>
          </Link>
        </div>
      </div>
    </PageLayout>
  )
}
