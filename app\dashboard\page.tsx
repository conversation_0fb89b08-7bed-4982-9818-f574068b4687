"use client"

import { useAuth } from "@/components/auth-provider"
import { PageLayout } from "@/components/page-layout"
import { LoadingSpinner } from "@/components/loading-spinner"
import { PropertyCard } from "@/components/property-card"

// Sample property data for St Cloud Enterprises
const properties = [
  {
    id: 1,
    name: "St Cloud Real Estate",
    description:
      "Premier real estate services offering residential and commercial properties across the region. Specializing in luxury homes, investment properties, and commercial developments.",
    imageUrl: "/modern-real-estate-office.png",
    establishedDate: "2018",
    url: "https://stcloudrealstate.com",
  },
  {
    id: 2,
    name: "Cloud Financial Services",
    description:
      "Comprehensive financial planning and investment management services. Providing wealth management, retirement planning, and corporate financial solutions.",
    imageUrl: "/placeholder-rnww4.png",
    establishedDate: "2020",
    url: "https://cloudfinancial.com",
  },
  {
    id: 3,
    name: "St Cloud Tech Solutions",
    description:
      "Innovative technology consulting and software development company. Delivering custom applications, cloud infrastructure, and digital transformation services.",
    imageUrl: "/placeholder-09wp3.png",
    establishedDate: "2019",
    url: "https://stcloudtech.com",
  },
  {
    id: 4,
    name: "Enterprise Marketing Hub",
    description:
      "Full-service digital marketing agency specializing in brand development, social media management, and performance marketing campaigns.",
    imageUrl: "/placeholder-qzld9.png",
    establishedDate: "2021",
    url: "https://enterprisemarketinghub.com",
  },
  {
    id: 5,
    name: "Cloud Logistics Network",
    description:
      "Efficient supply chain and logistics management solutions. Providing warehousing, distribution, and transportation services across multiple regions.",
    imageUrl: "/modern-warehouse-logistics.png",
    establishedDate: "2017",
    url: "https://cloudlogistics.com",
  },
  {
    id: 6,
    name: "St Cloud Healthcare Partners",
    description:
      "Integrated healthcare services and medical practice management. Offering primary care, specialized treatments, and healthcare technology solutions.",
    imageUrl: "/modern-medical-center.png",
    establishedDate: "2022",
    url: "https://stcloudhealthcare.com",
  },
]

export default function DashboardPage() {
  const { user, isLoading } = useAuth()

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!user) {
    return (
      <PageLayout title="Dashboard">
        <div className="text-center">
          <p className="text-muted-foreground">Please sign in to view the dashboard.</p>
        </div>
      </PageLayout>
    )
  }

  return (
    <PageLayout title="Dashboard">
      <div className="space-y-6">

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {properties.map((property) => (
            <PropertyCard
              key={property.id}
              name={property.name}
              description={property.description}
              imageUrl={property.imageUrl}
              establishedDate={property.establishedDate}
              url={property.url}
            />
          ))}
        </div>

        {properties.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No properties found.</p>
            <p className="text-sm text-muted-foreground mt-2">Properties will appear here when added.</p>
          </div>
        )}
      </div>
    </PageLayout>
  )
}
