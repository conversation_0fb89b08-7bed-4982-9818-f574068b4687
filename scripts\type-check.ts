#!/usr/bin/env npx tsx

/**
 * Enhanced TypeScript type checking script
 * Provides detailed information about what's being checked
 */

import { execSync } from 'child_process';
import { readdirSync, statSync } from 'fs';
import { join } from 'path';

interface TypeCheckOptions {
  watch?: boolean;
  verbose?: boolean;
}

function findTypeScriptFiles(dir: string, files: string[] = []): string[] {
  const items = readdirSync(dir);
  
  for (const item of items) {
    const fullPath = join(dir, item);
    const stat = statSync(fullPath);
    
    if (stat.isDirectory()) {
      // Skip node_modules and .next directories
      if (!['node_modules', '.next', '.git'].includes(item)) {
        findTypeScriptFiles(fullPath, files);
      }
    } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

function runTypeCheck(options: TypeCheckOptions = {}) {
  console.log('🔍 Starting TypeScript type checking...\n');
  
  if (options.verbose) {
    // Find and display all TypeScript files
    const tsFiles = findTypeScriptFiles(process.cwd());
    console.log(`📁 Found ${tsFiles.length} TypeScript files:`);
    tsFiles.forEach(file => {
      console.log(`   ${file.replace(process.cwd(), '.')}`);
    });
    console.log('');
  }
  
  try {
    const command = options.watch 
      ? 'tsc --noEmit --watch --pretty'
      : 'tsc --noEmit --pretty';
    
    console.log(`🚀 Running: ${command}\n`);
    
    const output = execSync(command, { 
      encoding: 'utf8',
      stdio: options.watch ? 'inherit' : 'pipe'
    });
    
    if (!options.watch) {
      if (output.trim()) {
        console.log(output);
      } else {
        console.log('✅ No type errors found!');
      }
    }
    
  } catch (error: any) {
    if (error.stdout) {
      console.log(error.stdout);
    }
    if (error.stderr) {
      console.error(error.stderr);
    }
    console.log('\n❌ Type checking failed!');
    process.exit(1);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const options: TypeCheckOptions = {
  watch: args.includes('--watch') || args.includes('-w'),
  verbose: args.includes('--verbose') || args.includes('-v')
};

// Run the type check (always run when script is executed)
runTypeCheck(options);

export { runTypeCheck, findTypeScriptFiles };
