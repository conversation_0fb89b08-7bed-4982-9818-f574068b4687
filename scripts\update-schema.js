#!/usr/bin/env node

/**
 * <PERSON>ript to update Drizzle schema from database
 * 
 * This script:
 * 1. Runs drizzle-kit pull to generate schema from database
 * 2. Moves the generated files from migrations/ to schemas/ directory
 * 3. Ensures proper project structure for imports
 */

import { execSync } from 'child_process';
import { existsSync, mkdirSync, copyFileSync, unlinkSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

function log(message) {
  console.log(`[update-schema] ${message}`);
}

function error(message) {
  console.error(`[update-schema] ERROR: ${message}`);
}

async function updateSchema() {
  try {
    log('Starting schema update process...');

    // Step 1: Run drizzle-kit pull
    log('Running drizzle-kit pull...');
    try {
      execSync('pnpm drizzle-kit pull', { 
        cwd: projectRoot, 
        stdio: 'inherit' 
      });
      log('✅ drizzle-kit pull completed successfully');
    } catch (err) {
      error('Failed to run drizzle-kit pull');
      throw err;
    }

    // Step 2: Check if generated files exist
    const migrationsDir = join(projectRoot, 'migrations');
    const schemaFile = join(migrationsDir, 'schema.ts');
    const relationsFile = join(migrationsDir, 'relations.ts');

    if (!existsSync(schemaFile)) {
      error('schema.ts not found in migrations directory');
      process.exit(1);
    }

    log('✅ Found generated schema.ts');

    // Check if relations.ts exists (it might not always be generated)
    const hasRelations = existsSync(relationsFile);
    if (hasRelations) {
      log('✅ Found generated relations.ts');
    } else {
      log('ℹ️  No relations.ts file generated (this is normal if no relations are defined)');
    }

    // Step 3: Ensure schemas directory exists
    const schemasDir = join(projectRoot, 'schemas');
    if (!existsSync(schemasDir)) {
      log('Creating schemas directory...');
      mkdirSync(schemasDir, { recursive: true });
    }

    // Step 4: Move files to schemas directory
    const targetSchemaFile = join(schemasDir, 'schema.ts');
    const targetRelationsFile = join(schemasDir, 'relations.ts');

    log('Moving schema.ts to schemas directory...');
    copyFileSync(schemaFile, targetSchemaFile);
    unlinkSync(schemaFile);
    log('✅ Moved schema.ts');

    if (hasRelations) {
      log('Moving relations.ts to schemas directory...');
      copyFileSync(relationsFile, targetRelationsFile);
      unlinkSync(relationsFile);
      log('✅ Moved relations.ts');
    }

    // Step 5: Verify files are in correct location
    if (existsSync(targetSchemaFile)) {
      log('✅ schema.ts is now available at schemas/schema.ts');
    } else {
      error('Failed to move schema.ts to schemas directory');
      process.exit(1);
    }

    if (hasRelations && existsSync(targetRelationsFile)) {
      log('✅ relations.ts is now available at schemas/relations.ts');
    }

    log('🎉 Schema update completed successfully!');
    log('');
    log('Files available for import:');
    log('  - schemas/schema.ts');
    if (hasRelations) {
      log('  - schemas/relations.ts');
    }

  } catch (err) {
    error(`Schema update failed: ${err.message}`);
    process.exit(1);
  }
}

// Run the script
updateSchema();
