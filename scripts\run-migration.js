import { neon } from '@neondatabase/serverless';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function runMigration() {
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error('DATABASE_URL environment variable is not set');
    process.exit(1);
  }

  console.log('Connecting to Neon database...');
  const sql = neon(databaseUrl);

  try {
    // Read the migration file
    const migrationPath = join(__dirname, '..', 'migrations', '0000_init.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf8');
    
    console.log('Executing migration script...');
    console.log('Migration content:');
    console.log(migrationSQL);
    
    // Split the SQL into individual statements and execute them
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    for (const statement of statements) {
      if (statement.trim()) {
        console.log(`Executing: ${statement.substring(0, 50)}...`);
        await sql(statement);
      }
    }

    console.log('Migration completed successfully!');
    
    // Verify tables were created
    console.log('\nVerifying tables...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `;
    
    console.log('Created tables:', tables.map(t => t.table_name));
    
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

runMigration();
