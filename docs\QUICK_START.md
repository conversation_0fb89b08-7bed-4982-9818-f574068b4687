# Quick Start Guide
## SCE Portal Development Setup

This guide gets you up and running with the SCE Portal project in under 15 minutes.

## 🚀 Prerequisites

- **Node.js**: Version 18+ 
- **pnpm**: Package manager (required, not npm/yarn)
- **Git**: Version control
- **GitHub Account**: With repository access

## ⚡ 5-Minute Setup

### 1. <PERSON>lone and Install
```bash
git clone https://github.com/stcloudenterprises/sce_portal.git
cd sce_portal
pnpm install
```

### 2. Environment Configuration
Create `.env.local` in the project root:
```bash
# Get copy of current environment variables
```

### 3. Start Development
```bash
pnpm dev
```
Visit: http://localhost:3000

## 🤖 AI Agent Setup

### Local Agent Configuration
Add to your `.env.local`:
```bash
# AI Agent Identity, fill in values
AI_AGENT_NAME=
AI_AGENT_EMAIL=
AI_AGENT_PAT=
GH_TOKEN="${AI_AGENT_PAT}"
```

### Agent Workflow Trigger
When starting a new task, begin your message with:
```
New Task: [description]
```
This triggers proper branch creation and Git attribution.

## 🔄 Development Workflow

### Standard Process
```bash
# 1. Update main branch
git checkout main
git pull origin main

# 2. Create feature branch
git checkout -b feature/your-feature-name

# 3. Make changes and commit with proper attribution
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit -m "feat: your changes"

# 4. Push and create PR
git push origin feature/your-feature-name
```

### Quality Assurance Commands
```bash
pnpm lint          # Fix ESLint errors
pnpm type-check    # TypeScript validation
pnpm test          # Run test suite
pnpm build         # Verify production build
```

## 🎯 Agent Capabilities

### Local Agents (Full Access)
- ✅ Complete API testing
- ✅ OAuth flow testing
- ✅ Email functionality testing
- ✅ Database operations
- ✅ Sensitive environment variables

### Remote Agents (Limited Access)
- ✅ UI/UX changes
- ✅ Component development
- ✅ TypeScript/JavaScript logic
- ✅ Build configuration
- ❌ API testing without secrets

## 🛠️ Essential Commands

### Package Management
```bash
pnpm install        # Install dependencies
pnpm add package    # Add new package
pnpm remove package # Remove package
```

### Development
```bash
pnpm dev            # Development server
pnpm build          # Production build
pnpm start          # Start production server
pnpm lint           # Run ESLint
pnpm type-check     # TypeScript check
pnpm test           # Run tests
```

### Git Operations
```bash
# Agent attribution
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit -m "message"

# Check attribution
git log --format=fuller

# Fix attribution
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit --amend --reset-author --no-edit
```

## 🚨 Common Issues

### Port Already in Use
```bash
# Kill process on port 3000
npx kill-port 3000
# Or use different port
pnpm dev -- -p 3001
```

### Environment Variables Not Loading
- Ensure `.env.local` is in project root
- Restart development server after changes
- Check for typos in variable names

### Git Attribution Issues
- Verify AI_AGENT_NAME and AI_AGENT_EMAIL in `.env.local`
- Use `-c` flags for temporary credential override
- Check commit history with `git log --format=fuller`

## 📚 Next Steps

### For Developers
1. Study [AI Agent Workflow](AI_AGENT_WORKFLOW.md) for comprehensive collaboration processes
2. Bookmark [Command Reference](COMMAND_REFERENCE.md) for quick access

### For AI Agents
1. Understand agent types and capabilities in [AI Agent Workflow](AI_AGENT_WORKFLOW.md)
2. Follow proper Git attribution procedures
3. Know when to use local vs remote agent capabilities

### For Troubleshooting
1. Check [Troubleshooting Guide](TROUBLESHOOTING.md) for detailed solutions
2. Verify environment configuration

## 🔗 Useful Links

- **Repository**: [stcloudenterprises/sce_portal](https://github.com/stcloudenterprises/sce_portal)
- **Next.js Docs**: [nextjs.org/docs](https://nextjs.org/docs)
- **Tailwind CSS**: [tailwindcss.com](https://tailwindcss.com/)

---

**Need more detailed information?** Check the [complete documentation index](README.md) for comprehensive guides and references.

**Having issues?** Consult the [Troubleshooting Guide](TROUBLESHOOTING.md) for solutions to common problems.
