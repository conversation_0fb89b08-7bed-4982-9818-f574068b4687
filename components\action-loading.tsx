"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { LoadingSpinner } from "@/components/loading-spinner"

interface ActionLoadingProps {
  isLoading: boolean
  delay?: number
  children: React.ReactNode
}

export function ActionLoading({ isLoading, delay = 500, children }: ActionLoadingProps) {
  const [showLoading, setShowLoading] = useState(false)

  useEffect(() => {
    let timeoutId: NodeJS.Timeout

    if (isLoading) {
      // Show loading indicator after specified delay (default 500ms)
      timeoutId = setTimeout(() => {
        setShowLoading(true)
      }, delay)
    } else {
      // Hide loading immediately when action completes
      clearTimeout(timeoutId)
      setShowLoading(false)
    }

    return () => {
      clearTimeout(timeoutId)
    }
  }, [isLoading, delay])

  if (showLoading) {
    return (
      <div className="flex items-center justify-center gap-2">
        <LoadingSpinner size="sm" />
        <span className="text-sm text-muted-foreground">Processing...</span>
      </div>
    )
  }

  return <>{children}</>
}
