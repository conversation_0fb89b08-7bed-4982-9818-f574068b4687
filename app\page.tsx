"use client"

import { useAuth } from "@/components/auth-provider"
import { PageLayout } from "@/components/page-layout"
import Link from "next/link"
import { LoadingSpinner } from "@/components/loading-spinner"

export default function HomePage() {
  const { user, isLoading } = useAuth()

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <PageLayout title="" showNavigation={false}>
      <div className="min-h-[60vh] flex items-center justify-center">
        <div className="text-center space-y-8">
          <Link href={user ? "/dashboard" : "/signin"}>
            <h1 className="text-6xl font-bold text-foreground hover:text-primary transition-colors cursor-pointer">
              St Cloud Enterprises Portal
            </h1>
          </Link>
        </div>
      </div>
    </PageLayout>
  )
}
