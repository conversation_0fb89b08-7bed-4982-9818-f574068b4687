# GitHub API Fields Reference
## SCE Portal GitHub Issue API Documentation

This reference documents all available fields when fetching GitHub Issues via the API. Use this guide when working with GitHub Issues through the github-api tool.

## 📋 Overview

This reference documents all available fields when fetching GitHub Issues via the API. Fields marked as **required** are always present, while optional fields may be null or undefined.

## 🏗️ Basic Issue Information

Core properties that identify and describe the issue.

| Field Name | Type | Required | Description | Example |
|------------|------|----------|-------------|---------|
| `id` | number | ✅ | Unique identifier for the issue | `1234567890` |
| `number` | number | ✅ | Issue number within the repository | `42` |
| `title` | string | ✅ | The title of the issue | `"Bug: Authentication fails on mobile devices"` |
| `body` | string | ❌ | The main content/description of the issue | `"When users try to authenticate on mobile devices, the OAuth flow fails..."` |
| `state` | string | ✅ | Current state of the issue | `"open"` |
| `url` | string | ✅ | API URL for the issue | `"https://api.github.com/repos/owner/repo/issues/42"` |

## 👥 User & Assignment Information

Information about who created and is assigned to the issue.

| Field Name | Type | Required | Description | Example |
|------------|------|----------|-------------|---------|
| `user` | object | ✅ | User who created the issue | See User Object Structure below |
| `assignee` | object \| null | ❌ | User assigned to the issue (null if unassigned) | See User Object Structure below |
| `assignees` | array | ✅ | Array of users assigned to the issue | `[{user_object}, ...]` |

### User Object Structure
```json
{
  "id": 12345,
  "login": "johndoe",
  "site_admin": false,
  "type": "User",
  "url": "https://api.github.com/users/johndoe",
  "user_view_type": "public"
}
```

## ⏰ Timestamps

Date and time information for issue lifecycle events.

| Field Name | Type | Required | Description | Example |
|------------|------|----------|-------------|---------|
| `created_at` | string (ISO 8601) | ✅ | When the issue was created | `"2025-01-15T10:30:00Z"` |
| `updated_at` | string (ISO 8601) | ✅ | When the issue was last updated | `"2025-01-20T14:45:30Z"` |

## 💬 Interaction Data

Information about comments, reactions, and user engagement.

| Field Name | Type | Required | Description | Example |
|------------|------|----------|-------------|---------|
| `comments` | number | ✅ | Number of comments on the issue | `5` |
| `reactions` | object | ✅ | Reaction counts for the issue | See Reactions Object Structure below |

### Reactions Object Structure
```json
{
  "+1": 3,
  "-1": 0,
  "laugh": 1,
  "hooray": 2,
  "confused": 0,
  "heart": 1,
  "rocket": 0,
  "eyes": 0,
  "total_count": 7
}
```

## 📊 Meta Information

Additional metadata and status information.

| Field Name | Type | Required | Description | Example |
|------------|------|----------|-------------|---------|
| `author_association` | string | ✅ | The author's association with the repository | `"OWNER"` |
| `locked` | boolean | ✅ | Whether the issue is locked for comments | `false` |

## 📋 Sub-Issues Summary

Information about related sub-issues or tasks.

| Field Name | Type | Required | Description | Example |
|------------|------|----------|-------------|---------|
| `sub_issues_summary` | object | ❌ | Summary of sub-issues or task completion | See Sub-Issues Object Structure below |

### Sub-Issues Object Structure
```json
{
  "completed": 3,
  "percent_completed": 60,
  "total": 5
}
```

## 📝 Important Notes

- **Required Fields**: Fields marked with ✅ are required and always present
- **Optional Fields**: Fields marked with ❌ may be null or undefined
- **Object Fields**: Object fields may contain additional properties not listed here
- **Date Format**: Date strings follow ISO 8601 format (YYYY-MM-DDTHH:mm:ssZ)
- **Permissions**: Some fields may be null or undefined depending on issue state and permissions

## 🔗 Related Documentation

- **[AI Agent Workflow](AI_AGENT_WORKFLOW.md)** - Learn how to use the github-api tool effectively
- **[Command Reference](COMMAND_REFERENCE.md)** - Essential development commands
- **[Quick Start Guide](QUICK_START.md)** - Get started with the SCE Portal project

## 🆘 Need Help?

- **Common Issues**: Check [Troubleshooting Guide](TROUBLESHOOTING.md)
- **Setup Problems**: Review [Authentication Setup](AUTHENTICATION_SETUP.md)
- **Workflow Questions**: Consult [AI Agent Workflow](AI_AGENT_WORKFLOW.md)

---

**Last Updated**: July 31, 2025  
**Maintainer**: Kevin Lonigro  
**Project**: SCE Portal  
**Repository**: [stcloudenterprises/sce_portal](https://github.com/stcloudenterprises/sce_portal)
