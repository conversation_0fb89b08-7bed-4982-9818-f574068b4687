"use client"

import { createContext, use<PERSON>ontext, useState, useEffect, type ReactNode } from "react"

interface User {
  id: string
  email: string
  name: string
}

interface AuthContextType {
  user: User | null
  isLoading: boolean
  signIn: (email: string, password: string) => Promise<boolean>
  signOut: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check for existing session on mount
    const savedUser = localStorage.getItem("st-cloud-user")
    if (savedUser) {
      setUser(JSON.parse(savedUser))
    } else {
      // Also check cookie as fallback
      const cookieUser = document.cookie
        .split('; ')
        .find(row => row.startsWith('st-cloud-user='))
        ?.split('=')[1]

      if (cookieUser) {
        try {
          const user = JSON.parse(decodeURIComponent(cookieUser))
          setUser(user)
          localStorage.setItem("st-cloud-user", JSON.stringify(user))
        } catch (error) {
          console.error("Error parsing user cookie:", error)
        }
      }
    }
    setIsLoading(false)
  }, [])

  const signIn = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true)

    // Simulate API call delay
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Simple demo authentication - in production, this would be a real API call
    if (email === "<EMAIL>" && password === "StCloud2024!@#") {
      const user = {
        id: "1",
        email: "<EMAIL>",
        name: "Admin User",
      }
      setUser(user)
      localStorage.setItem("st-cloud-user", JSON.stringify(user))

      // Set cookie for middleware authentication check
      document.cookie = `st-cloud-user=${encodeURIComponent(JSON.stringify(user))}; path=/; max-age=${60 * 60 * 24 * 7}` // 7 days

      setIsLoading(false)
      return true
    }

    setIsLoading(false)
    return false
  }

  const signOut = () => {
    setUser(null)
    localStorage.removeItem("st-cloud-user")

    // Clear the authentication cookie
    document.cookie = "st-cloud-user=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT"
  }

  return <AuthContext.Provider value={{ user, isLoading, signIn, signOut }}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
