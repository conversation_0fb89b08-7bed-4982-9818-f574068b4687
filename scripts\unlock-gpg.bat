@echo off
REM GPG Key Unlock Script for Agent Signing
REM This script unlocks the agent's GPG key for automated git signing
REM Updated: 2025-08-03 - Added GPG agent configuration with extended cache
REM Fixed: Script logic to prevent showing both success and error messages

echo ========================================
echo GPG Agent Key Unlock for Git Signing
echo ========================================
echo.

REM Step 1: Check if GPG agent is running
echo [1/3] Checking GPG agent status...
gpg-connect-agent "getinfo version" /bye >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo GPG agent is not running. Starting GPG agent...
    gpg-agent --daemon --enable-ssh-support
    timeout /t 2 /nobreak >nul
    echo GPG agent started.
) else (
    echo GPG agent is running.
)

REM Step 2: Verify GPG agent configuration
echo.
echo [2/3] Verifying GPG agent configuration...
if exist "%APPDATA%\gnupg\gpg-agent.conf" (
    echo GPG agent configuration found.
    echo Configuration settings:
    type "%APPDATA%\gnupg\gpg-agent.conf"
) else (
    echo WARNING: GPG agent configuration not found!
)

REM Step 3: Test GPG key unlock
echo.
echo [3/3] Testing GPG key unlock...
echo This will unlock the agent's GPG key: 0E0C2F7313B1CA15
echo You will be prompted for the agent's passphrase.
echo After unlocking, the key will be cached for 8 hours.
echo.
echo Agent GPG key unlock test | gpg --clearsign --default-key 0E0C2F7313B1CA15 >nul 2>&1

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo SUCCESS: Agent GPG key is now unlocked!
    echo ========================================
    echo.
    echo The key will remain cached for:
    echo - 8 hours of activity ^(default-cache-ttl^)
    echo - Up to 24 hours maximum ^(max-cache-ttl^)
    echo.
    echo Git commits can now use GPG signing automatically.
    echo.
    goto :end
) else (
    echo.
    echo ========================================
    echo ERROR: Failed to unlock GPG key
    echo ========================================
    echo.
    echo Please check:
    echo - GPG key exists and is valid
    echo - Correct passphrase was entered
    echo - GPG agent is running
    echo.
)

:end

pause
