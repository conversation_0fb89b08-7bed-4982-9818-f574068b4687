import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ExternalLink } from "lucide-react"

interface PropertyCardProps {
  name: string
  description: string
  imageUrl: string
  establishedDate: string
  url?: string
}

export function PropertyCard({ name, description, imageUrl, establishedDate, url }: PropertyCardProps) {
  return (
    <Card className="h-full flex flex-col hover:shadow-lg transition-shadow">
      <CardHeader>
        <CardTitle className="text-lg">{name}</CardTitle>
      </CardHeader>

      <CardContent className="flex-1 space-y-4">
        <div className="aspect-video relative overflow-hidden rounded-md bg-muted">
          <img
            src={imageUrl || "/placeholder.svg"}
            alt={`${name} preview`}
            className="object-cover w-full h-full"
            onError={(e) => {
              const target = e.target as HTMLImageElement
              target.src = `/placeholder.svg?height=200&width=300&query=${encodeURIComponent(name + " website preview")}`
            }}
          />
        </div>

        <CardDescription className="text-sm leading-relaxed">{description}</CardDescription>

        {url && (
          <Button variant="outline" size="sm" className="w-full bg-transparent" asChild>
            <a href={url} target="_blank" rel="noopener noreferrer">
              <ExternalLink className="w-4 h-4 mr-2" />
              Visit Site
            </a>
          </Button>
        )}
      </CardContent>

      <CardFooter className="pt-4">
        <div className="w-full text-right">
          <p className="text-sm text-muted-foreground">Established: {establishedDate}</p>
        </div>
      </CardFooter>
    </Card>
  )
}
