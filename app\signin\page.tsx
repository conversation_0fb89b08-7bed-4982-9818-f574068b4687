"use client"

import type React from "react"

import { useState } from "react"
import { useAuth } from "@/components/auth-provider"
import { PageLayout } from "@/components/page-layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import { LoadingSpinner } from "@/components/loading-spinner"
import { ActionLoading } from "@/components/action-loading"

export default function SignInPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { signIn } = useAuth()
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setIsSubmitting(true)

    try {
      const success = await signIn(email, password)

      if (success) {
        setTimeout(() => {
          router.push("/dashboard")
        }, 100)
      } else {
        setError("Invalid email or password")
      }
    } catch (error) {
      setError("An error occurred during sign in")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <PageLayout title="Sign In" showNavigation={false}>
      <div className="max-w-md mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Sign In</CardTitle>
            <CardDescription>Enter your credentials to access the St Cloud Enterprises Portal</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  placeholder="<EMAIL>"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  placeholder="StCloud2024!@#"
                />
              </div>

              {error && <div className="text-destructive text-sm">{error}</div>}

              <ActionLoading isLoading={isSubmitting}>
                <Button type="submit" className="w-full" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Signing In...
                    </>
                  ) : (
                    "Sign In"
                  )}
                </Button>
              </ActionLoading>
            </form>

            <div className="mt-4 text-sm text-muted-foreground">
              <p>Demo credentials:</p>
              <p>Email: <EMAIL></p>
              <p>Password: StCloud2024!@#</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </PageLayout>
  )
}
