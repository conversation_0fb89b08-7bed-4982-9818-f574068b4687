import { PageLayout } from "@/components/page-layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"

export default function NotFound() {
  return (
    <PageLayout title="Page Not Found" showNavigation={false}>
      <div className="text-center space-y-6 py-12">
        <div className="space-y-2">
          <h1 className="text-6xl font-bold text-muted-foreground">404</h1>
          <h2 className="text-2xl font-semibold text-foreground">Page Not Found</h2>
          <p className="text-muted-foreground max-w-md mx-auto">
            The page you are looking for does not exist or has been moved.
          </p>
        </div>

        <div className="flex justify-center gap-4">
          <Link href="/">
            <Button>Go Home</Button>
          </Link>
          <Link href="/dashboard">
            <Button variant="outline">Dashboard</Button>
          </Link>
        </div>
      </div>
    </PageLayout>
  )
}
