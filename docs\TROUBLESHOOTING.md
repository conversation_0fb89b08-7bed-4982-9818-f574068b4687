# Troubleshooting Guide
## SCE Portal Common Issues & Solutions

This guide covers common problems encountered during development and their solutions.

## 🚨 Quick Fixes

### Port Already in Use
**Problem**: `Error: listen EADDRINUSE: address already in use :::3000`

**Solutions**:
```bash
# Option 1: Kill process on port 3000
npx kill-port 3000

# Option 2: Find and kill process manually
lsof -i :3000                   # Find process ID
kill -9 <PID>                   # Kill specific process

# Option 3: Use different port
pnpm dev -- -p 3001
```

### Git Attribution Issues
**Problem**: Commits showing wrong author

**Solutions**:
```bash
# Check current attribution
git log --format=fuller -n 3

# Fix last commit
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit --amend --reset-author --no-edit

# Force push corrected commit
git push --force origin branch-name

# Verify environment variables
echo $AI_AGENT_NAME
echo $AI_AGENT_EMAIL
```

## 🔧 Development Issues

### Build Failures

#### TypeScript Errors
**Problem**: `Type error: Property 'x' does not exist on type 'y'`

**Solutions**:
```bash
# 1. Run type check
pnpm type-check

# 2. Check for missing types
pnpm add -D @types/package-name

# 3. Restart TypeScript server in IDE
# VS Code: Ctrl+Shift+P → "TypeScript: Restart TS Server"

# 4. Clear Next.js cache
rm -rf .next
pnpm build
```

#### ESLint Errors
**Problem**: Build fails due to linting errors

**Solutions**:
```bash
# 1. Check specific errors
pnpm lint

# 2. Auto-fix where possible
pnpm lint:fix

# 3. Disable specific rules (if necessary)
// eslint-disable-next-line @typescript-eslint/no-unused-vars

# 4. Update ESLint configuration
# Edit .eslintrc.json if needed
```

### Package Management Issues

#### pnpm Installation Failures
**Problem**: `ERR_PNPM_PEER_DEP_ISSUES`

**Solutions**:
```bash
# 1. Clear pnpm cache
pnpm store prune

# 2. Delete node_modules and reinstall
rm -rf node_modules pnpm-lock.yaml
pnpm install

# 3. Use legacy peer deps (if needed)
pnpm install --legacy-peer-deps

# 4. Check Node.js version
node --version  # Should be 18+
```

#### Wrong Package Manager Used
**Problem**: `npm` or `yarn` used instead of `pnpm`

**Solutions**:
```bash
# Remove other lock files
rm -f package-lock.json yarn.lock

# Use pnpm exclusively
pnpm install
pnpm dev

# Add to .npmrc to enforce pnpm
echo "engine-strict=true" >> .npmrc
```

## 🌐 Network & API Issues

### API Route Failures
**Problem**: `/api/auth/*` routes return 500 errors

**Solutions**:
```bash
# 1. Check server logs
pnpm dev                        # Look for error messages
```

## 🔄 Git & Version Control Issues

### Branch Synchronization Problems
**Problem**: Local branch out of sync with remote

**Solutions**:
```bash
# 1. Check branch status
git status
git log --oneline --graph --decorate

# 2. Sync with remote
git fetch origin
git pull origin main

# 3. Reset to remote state (if needed)
git reset --hard origin/main

# 4. Force push (use carefully)
git push --force origin branch-name
```

### Merge Conflicts
**Problem**: Git merge conflicts during pull/merge

**Solutions**:
```bash
# 1. Check conflict status
git status

# 2. View conflicted files
git diff --name-only --diff-filter=U

# 3. Resolve conflicts manually
# Edit files to resolve <<<<<<< ======= >>>>>>> markers

# 4. Mark as resolved and commit
git add .
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit -m "resolve: merge conflicts"
```

### Remote Repository Issues
**Problem**: `Permission denied` or authentication failures

**Solutions**:
```bash
# 1. Check remote URL
git remote -v

# 2. Verify GitHub authentication
echo $AI_AGENT_PAT               # Should show token

# 3. Test GitHub API access
curl -H "Authorization: token $AI_AGENT_PAT" https://api.github.com/user

# 4. Update remote URL if needed
git remote set-url origin https://github.com/stcloudenterprises/sce_portal.git
```

## 🖥️ Environment-Specific Issues

### Windows PowerShell Issues
**Problem**: Commands fail due to execution policy

**Solutions**:
```bash
# Use direct pnpm commands (not PowerShell cmdlets)
pnpm dev                        # ✅ Correct
Get-Process                     # ❌ Avoid PowerShell cmdlets

# Check execution policy (if needed)
Get-ExecutionPolicy

# Use cross-platform commands
ls                              # Instead of Get-ChildItem
pwd                             # Instead of Get-Location
```

### VS Code Integration Issues
**Problem**: TypeScript/ESLint not working in editor

**Solutions**:
```bash
# 1. Restart TypeScript server
# Ctrl+Shift+P → "TypeScript: Restart TS Server"

# 2. Reload VS Code window
# Ctrl+Shift+P → "Developer: Reload Window"

# 3. Check workspace settings
# Verify .vscode/settings.json configuration

# 4. Install required extensions
# - TypeScript and JavaScript Language Features
# - ESLint
# - Prettier
```

## 🧪 Testing Issues

### Test Failures
**Problem**: Tests fail unexpectedly

**Solutions**:
```bash
# 1. Run tests with verbose output
pnpm test -- --verbose

# 2. Run specific test file
pnpm test -- --testPathPattern="auth"

# 3. Clear test cache
pnpm test -- --clearCache

# 4. Check for async issues
pnpm test -- --detectOpenHandles

# 5. Update test snapshots (if needed)
pnpm test -- --updateSnapshot
```

## 🚨 Emergency Procedures

### Complete Reset
**When everything is broken**:
```bash
# 1. Stash current changes
git stash

# 2. Reset to clean main
git checkout main
git reset --hard origin/main
git pull origin main

# 3. Clean dependencies
rm -rf node_modules .next pnpm-lock.yaml
pnpm install

# 4. Restart development
pnpm dev
```

### Security Incident Response
**If secrets are accidentally committed**:
```bash
# 1. IMMEDIATELY rotate exposed credentials
# - Change OAuth client secrets
# - Generate new API keys
# - Update production environment variables

# 2. Remove from Git history (if needed)
# See AI_AGENT_WORKFLOW.md Appendix B for git-filter-repo procedures

# 3. Force push cleaned history
git push --force --mirror origin

# 4. Coordinate with all team members
# Everyone must re-clone or clean their local repositories
```

## 📞 Getting Help

### Debug Information to Collect
```bash
# System information
node --version
pnpm --version
git --version

# Project status
git status
git log --oneline -n 5
pnpm list --depth=0
```

### Where to Find More Help
- **Project Documentation**: [AI Agent Workflow](AI_AGENT_WORKFLOW.md)
- **Command Help**: [Command Reference](COMMAND_REFERENCE.md)
- **Next.js Issues**: [Next.js Documentation](https://nextjs.org/docs)
---

**Still having issues?** Document the problem with the debug information above and consult the [AI Agent Workflow](AI_AGENT_WORKFLOW.md) for comprehensive troubleshooting procedures.
