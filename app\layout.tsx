import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { GeistSans } from "geist/font/sans"
import { GeistMono } from "geist/font/mono"
import "./globals.css"
import { AuthProvider } from "@/components/auth-provider"
import { NavigationLoading } from "@/components/navigation-loading"

export const metadata: Metadata = {
  title: "St Cloud Enterprises Portal",
  description: "Managing access to St Cloud Enterprises properties",
  generator: "v0.app",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <head>
        <style>{`
html {
  font-family: ${GeistSans.style.fontFamily};
  --font-sans: ${GeistSans.variable};
  --font-mono: ${GeistMono.variable};
}
        `}</style>
      </head>
      <body>
        <AuthProvider>
          {/* Added global navigation loading indicator */}
          <NavigationLoading />
          {children}
        </AuthProvider>
      </body>
    </html>
  )
}
