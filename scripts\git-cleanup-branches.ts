#!/usr/bin/env node

import { execSync } from 'child_process';

/**
 * Git Branch Cleanup Script
 * 
 * This script safely deletes local branches that have been merged into main.
 * It excludes the current branch and the main branch itself.
 */

function executeCommand(command: string): string {
  try {
    return execSync(command, { encoding: 'utf8' }).trim();
  } catch (error) {
    console.error(`Error executing command: ${command}`);
    console.error(error);
    process.exit(1);
  }
}

function getCurrentBranch(): string {
  return executeCommand('git branch --show-current');
}

function getMergedBranches(): string[] {
  const output = executeCommand('git branch --merged main');
  return output
    .split('\n')
    .map(branch => branch.trim())
    .filter(branch => branch && !branch.startsWith('*'))
    .filter(branch => branch !== 'main');
}

function deleteBranch(branchName: string): void {
  try {
    executeCommand(`git branch -d "${branchName}"`);
    console.log(`✅ Deleted branch: ${branchName}`);
  } catch (error) {
    console.error(`❌ Failed to delete branch: ${branchName}`);
    console.error(error);
  }
}

function main(): void {
  console.log('🧹 Git Branch Cleanup Tool\n');
  
  // Get current branch
  const currentBranch = getCurrentBranch();
  console.log(`Current branch: ${currentBranch}`);
  
  // Get merged branches
  const mergedBranches = getMergedBranches();
  
  if (mergedBranches.length === 0) {
    console.log('✨ No merged branches to clean up!');
    return;
  }
  
  console.log(`\nFound ${mergedBranches.length} merged branch(es) to delete:`);
  mergedBranches.forEach(branch => console.log(`  - ${branch}`));
  
  console.log('\nDeleting merged branches...\n');
  
  // Delete each merged branch
  mergedBranches.forEach(deleteBranch);
  
  console.log('\n🎉 Branch cleanup complete!');
  
  // Show remaining branches
  console.log('\nRemaining local branches:');
  const remainingBranches = executeCommand('git branch');
  console.log(remainingBranches);
}

// Run the script
main();
