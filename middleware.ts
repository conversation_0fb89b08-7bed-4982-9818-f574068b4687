import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Protected routes that require authentication
  const protectedRoutes = ["/dashboard", "/account"]

  // Check if the current path is a protected route
  const isProtectedRoute = protectedRoutes.some((route) => pathname.startsWith(route))

  if (isProtectedRoute) {
    // Check for user session (in a real app, this would be a JWT or session token)
    const userSession = request.cookies.get("st-cloud-user")

    if (!userSession) {
      // Redirect to sign-in page if not authenticated
      return NextResponse.redirect(new URL("/signin", request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: ["/dashboard/:path*", "/account/:path*"],
}
