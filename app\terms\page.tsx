import { PageLayout } from "@/components/page-layout"

export default function TermsPage() {
  return (
    <PageLayout title="Terms of Service">
      <div className="max-w-4xl mx-auto prose prose-zinc dark:prose-invert">
        <div className="space-y-6">
          <section>
            <h2 className="text-xl font-semibold mb-3">Acceptance of Terms</h2>
            <p className="text-muted-foreground leading-relaxed">
              By accessing and using the St Cloud Enterprises Portal, you accept and agree to be bound by the terms and
              provision of this agreement.
            </p>
          </section>

          <section>
            <h2 className="text-xl font-semibold mb-3">Use License</h2>
            <p className="text-muted-foreground leading-relaxed">
              Permission is granted to temporarily access the materials on St Cloud Enterprises Portal for personal,
              non-commercial transitory viewing only. This is the grant of a license, not a transfer of title.
            </p>
          </section>

          <section>
            <h2 className="text-xl font-semibold mb-3">User Account</h2>
            <p className="text-muted-foreground leading-relaxed">
              You are responsible for maintaining the confidentiality of your account and password and for restricting
              access to your computer. You agree to accept responsibility for all activities that occur under your
              account or password.
            </p>
          </section>

          <section>
            <h2 className="text-xl font-semibold mb-3">Prohibited Uses</h2>
            <p className="text-muted-foreground leading-relaxed">
              You may not use our service for any illegal or unauthorized purpose nor may you, in the use of the
              service, violate any laws in your jurisdiction.
            </p>
          </section>

          <section>
            <h2 className="text-xl font-semibold mb-3">Limitation of Liability</h2>
            <p className="text-muted-foreground leading-relaxed">
              In no event shall St Cloud Enterprises or its suppliers be liable for any damages arising out of the use
              or inability to use the materials on the portal.
            </p>
          </section>

          <section>
            <h2 className="text-xl font-semibold mb-3">Contact Information</h2>
            <p className="text-muted-foreground leading-relaxed">
              If you have any questions about these Terms of Service, please contact <NAME_EMAIL>.
            </p>
          </section>

          <div className="text-sm text-muted-foreground mt-8 pt-6 border-t">
            <p>Last updated: {new Date().toLocaleDateString()}</p>
          </div>
        </div>
      </div>
    </PageLayout>
  )
}
