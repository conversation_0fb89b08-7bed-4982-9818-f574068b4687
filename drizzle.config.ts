import { defineConfig } from 'drizzle-kit'
import { config } from "dotenv";

config({ path: ".env.local" });

const url = process.env.DATABASE_URL

if (!url) throw new Error('DATABASE_URL environment variable not found.')

export default defineConfig({
   dialect: 'postgresql',
   dbCredentials: { url },
   out: './migrations',
   /* used for the generate command to create schema from existing migrations */
   schema: './schemas/schema.ts',
})
